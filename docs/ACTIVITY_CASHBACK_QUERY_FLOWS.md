# Activity Cashback Query Flows

## Overview

This document describes the query flows and API interactions for the improved Activity Cashback system with the new Task Identifier architecture.

## System Architecture Flow

```mermaid
graph TD
    A[GraphQL Query] --> B[Activity Cashback Service]
    B --> C{Task Processing}
    C --> D[Task Registry]
    C --> E[Legacy Processors]
    D --> F[Task Handlers]
    F --> G[Task Completion]
    E --> G
    G --> H[Database Update]
    H --> I[Response]
```

## Core Query Examples

### 1. Get User Dashboard

**GraphQL Query:**
```graphql
query GetUserDashboard($userId: ID!) {
  userDashboard(userId: $userId) {
    userInfo {
      userId
      currentTier
      totalPoints
      monthlyVolume
      nextTierRequirement
    }
    taskCenter {
      categories {
        id
        name
        displayName
        tasks {
          id
          name
          taskIdentifier
          description
          points
          taskType
          frequency
          isActive
          progress {
            id
            status
            completionCount
            lastCompletedAt
            streak
          }
        }
      }
      completedToday
      pointsEarnedToday
      streakTasks {
        taskId
        streak
        maxStreak
      }
    }
  }
}
```

**Flow Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant GraphQL
    participant ActivityService
    participant TaskRegistry
    participant Database

    Client->>GraphQL: GetUserDashboard query
    GraphQL->>ActivityService: GetUserDashboard(userId)
    ActivityService->>Database: Get user tier info
    ActivityService->>Database: Get task categories
    ActivityService->>Database: Get user task progress
    ActivityService->>TaskRegistry: Get task definitions
    TaskRegistry-->>ActivityService: Task handlers info
    Database-->>ActivityService: User data
    ActivityService-->>GraphQL: Dashboard data
    GraphQL-->>Client: Response
```

### 2. Complete Task

**GraphQL Mutation:**
```graphql
mutation CompleteTask($userId: ID!, $taskId: ID!, $verificationData: JSON) {
  completeTask(userId: $userId, taskId: $taskId, verificationData: $verificationData) {
    success
    message
    pointsAwarded
    newTier
    task {
      id
      name
      taskIdentifier
      progress {
        status
        completionCount
        lastCompletedAt
      }
    }
  }
}
```

**Flow with New Task System:**
```mermaid
sequenceDiagram
    participant Client
    participant GraphQL
    participant TaskCompatibility
    participant TaskRegistry
    participant TaskHandler
    participant Database

    Client->>GraphQL: CompleteTask mutation
    GraphQL->>TaskCompatibility: ProcessTask(userId, task, data)
    
    alt Task has identifier
        TaskCompatibility->>TaskRegistry: ProcessTask(task)
        TaskRegistry->>TaskHandler: Handle(task)
        TaskHandler->>Database: CompleteProgress(userId, taskId)
    else Fallback to legacy
        TaskCompatibility->>TaskCompatibility: ProcessTaskByName(taskName)
        TaskCompatibility->>Database: Legacy completion
    end
    
    Database-->>TaskHandler: Success
    TaskHandler-->>TaskRegistry: Completion result
    TaskRegistry-->>TaskCompatibility: Result
    TaskCompatibility-->>GraphQL: Task completed
    GraphQL-->>Client: Response
```

### 3. Process Trading Event

**Internal API Call:**
```go
// Trading event processing
tradeData := map[string]interface{}{
    "trade_type": "MEME",
    "volume": 1000.0,
    "user_id": userID,
    "timestamp": time.Now(),
}

err := taskCompatibility.ProcessTradingEvent(ctx, userID, tradeData)
```

**Flow Diagram:**
```mermaid
sequenceDiagram
    participant TradingSystem
    participant TaskCompatibility
    participant TaskRegistry
    participant MemeTradeHandler
    participant TradingPointsHandler
    participant Database

    TradingSystem->>TaskCompatibility: ProcessTradingEvent(userID, tradeData)
    
    Note over TaskCompatibility: Process MEME trade task
    TaskCompatibility->>TaskRegistry: ProcessTaskByIdentifier(MEME_TRADE_DAILY)
    TaskRegistry->>MemeTradeHandler: Handle(task, tradeData)
    MemeTradeHandler->>MemeTradeHandler: Validate trade data
    MemeTradeHandler->>Database: CompleteProgress(userID, taskID)
    
    Note over TaskCompatibility: Process trading points
    TaskCompatibility->>TaskRegistry: ProcessTaskByIdentifier(TRADING_POINTS)
    TaskRegistry->>TradingPointsHandler: Handle(task, tradeData)
    TradingPointsHandler->>Database: Update points based on volume
    
    Note over TaskCompatibility: Process accumulated trading
    loop For each accumulated trading task
        TaskCompatibility->>TaskRegistry: ProcessTaskByIdentifier(ACCUMULATED_TRADING_*)
        TaskRegistry->>TaskCompatibility: Check volume thresholds
    end
    
    Database-->>TaskCompatibility: All tasks processed
    TaskCompatibility-->>TradingSystem: Success
```

## Task Processing Flows

### Daily Check-in Flow

```mermaid
flowchart TD
    A[User clicks check-in] --> B[GraphQL mutation]
    B --> C[TaskCompatibility.ProcessTask]
    C --> D{Task has identifier?}
    D -->|Yes| E[TaskRegistry.ProcessTask]
    D -->|No| F[Map name to identifier]
    F --> E
    E --> G[DailyCheckinHandler.Handle]
    G --> H[UpdateActivity]
    H --> I[CompleteProgress]
    I --> J[Award points]
    J --> K[Update streak]
    K --> L[Return success]
```

### Trading Task Flow

```mermaid
flowchart TD
    A[Trade executed] --> B[Trading event triggered]
    B --> C[TaskCompatibility.ProcessTradingEvent]
    C --> D[Identify trade type]
    D -->|MEME| E[Process MEME trade task]
    D -->|PERPETUAL| F[Process PERPETUAL trade task]
    E --> G[MemeTradeHandler.Handle]
    F --> H[PerpetualTradeHandler.Handle]
    G --> I[Validate trade data]
    H --> I
    I --> J[Check daily completion]
    J -->|Not completed| K[CompleteProgress]
    J -->|Already completed| L[Skip]
    K --> M[Award 200 points]
    M --> N[Process trading points]
    N --> O[Calculate volume-based points]
    O --> P[Check accumulated trading milestones]
    P --> Q[Return results]
    L --> Q
```

## Error Handling Flows

### Task Processing Error Flow

```mermaid
flowchart TD
    A[Task processing starts] --> B{Task identifier exists?}
    B -->|Yes| C[Try TaskRegistry.ProcessTask]
    B -->|No| D[Try name mapping]
    D --> E{Mapping found?}
    E -->|Yes| C
    E -->|No| F[Use legacy processor]
    C --> G{Processing successful?}
    G -->|Yes| H[Return success]
    G -->|No| I[Log warning]
    I --> F[Fallback to legacy]
    F --> J{Legacy successful?}
    J -->|Yes| K[Return success with warning]
    J -->|No| L[Return error]
```

## Migration Flow

### Backward Compatibility Flow

```mermaid
flowchart TD
    A[Task request] --> B[TaskCompatibilityLayer]
    B --> C{Task has TaskIdentifier?}
    C -->|Yes| D[Use new TaskRegistry]
    C -->|No| E[Check LegacyTaskNameMap]
    E --> F{Name mapping exists?}
    F -->|Yes| G[Set temporary identifier]
    F -->|No| H[Use legacy processors]
    G --> D
    D --> I{Registry processing successful?}
    I -->|Yes| J[Update task with identifier]
    I -->|No| K[Log error and fallback]
    K --> H
    H --> L{Legacy successful?}
    L -->|Yes| M[Return success]
    L -->|No| N[Return error]
    J --> M
```

## Performance Monitoring

### Key Metrics to Track

1. **Task Processing Success Rate**
   - Registry-based processing: `registry_success_rate`
   - Legacy fallback rate: `legacy_fallback_rate`

2. **Response Times**
   - Average task completion time: `task_completion_time_avg`
   - 95th percentile: `task_completion_time_p95`

3. **Error Rates**
   - Task processing errors: `task_processing_errors`
   - Fallback failures: `fallback_failures`

### Monitoring Query Examples

```graphql
# Get system health metrics
query GetSystemMetrics {
  systemMetrics {
    taskProcessingStats {
      registrySuccessRate
      legacyFallbackRate
      averageProcessingTime
      errorRate
    }
    compatibilityStats {
      registryHandlers
      legacyProcessors
      identifierMappings
    }
  }
}
```

## Best Practices

### 1. Task Identifier Usage

```go
// ✅ Good: Use constants
err := registry.ProcessTaskByIdentifier(ctx, userID, model.TaskIDMemeTradeDaily, "daily", data)

// ❌ Bad: Hardcoded strings
err := registry.ProcessTaskByName(ctx, userID, "Complete one meme trade", "daily", data)
```

### 2. Error Handling

```go
// ✅ Good: Handle both new and legacy errors
if err := taskCompatibility.ProcessTask(ctx, userID, task, data); err != nil {
    log.Error("Task processing failed", zap.Error(err), zap.String("task_id", task.ID.String()))
    return fmt.Errorf("failed to process task: %w", err)
}
```

### 3. Data Validation

```go
// ✅ Good: Validate data before processing
func (h *MemeTradeHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
    volume, ok := data["volume"].(float64)
    if !ok || volume <= 0 {
        return fmt.Errorf("invalid MEME trade volume")
    }
    
    tradeType, ok := data["trade_type"].(string)
    if !ok || tradeType != "MEME" {
        return fmt.Errorf("invalid trade type for MEME task")
    }
    
    // Process task...
}
```

## Troubleshooting

### Common Issues

1. **Task not processing**: Check if task has correct identifier or name mapping
2. **Legacy fallback**: Monitor logs for "Using legacy processor system" messages
3. **Performance issues**: Check if too many tasks are falling back to legacy system

### Debug Queries

```graphql
# Check task configuration
query DebugTask($taskId: ID!) {
  task(id: $taskId) {
    id
    name
    taskIdentifier
    taskType
    frequency
    isActive
  }
}

# Check user progress
query DebugUserProgress($userId: ID!, $taskId: ID!) {
  userTaskProgress(userId: $userId, taskId: $taskId) {
    status
    completionCount
    lastCompletedAt
    streak
  }
}
```
