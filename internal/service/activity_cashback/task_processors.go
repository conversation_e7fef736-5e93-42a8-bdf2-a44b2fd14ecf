package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// TaskProcessor defines the interface for processing different types of tasks
type TaskProcessor interface {
	ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error
	CanProcess(task *model.ActivityTask) bool
	GetTaskType() string
}

// DailyTaskProcessor handles daily tasks
type DailyTaskProcessor struct {
	service ActivityCashbackServiceInterface
}

// NewDailyTaskProcessor creates a new DailyTaskProcessor
func NewDailyTaskProcessor(service ActivityCashbackServiceInterface) TaskProcessor {
	return &DailyTaskProcessor{
		service: service,
	}
}

// ProcessTask processes daily tasks
func (p *DailyTaskProcessor) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	switch task.Name {
	case "Daily Check-in":
		return p.processDailyCheckIn(ctx, userID, task)
	case "Complete one meme trade", "Complete 1 MEME Trade":
		return p.processMemeTrade(ctx, userID, task, data)
	case "Complete one derivatives trade", "Complete 1 Perpetual Trade":
		return p.processPerpetualTrade(ctx, userID, task, data)
	case "View market page", "Check Market Conditions":
		return p.processMarketCheck(ctx, userID, task)
	case "Consecutive Check-in (3/7/30 days)":
		return p.processConsecutiveCheckIn(ctx, userID, task, data)
	case "Trade for 3/7/15/30 consecutive days":
		return p.processConsecutiveTradingDays(ctx, userID, task, data)
	default:
		return fmt.Errorf("unknown daily task: %s", task.Name)
	}
}

// CanProcess checks if this processor can handle the task
func (p *DailyTaskProcessor) CanProcess(task *model.ActivityTask) bool {
	return task.TaskType == model.TaskTypeDaily
}

// GetTaskType returns the task type this processor handles
func (p *DailyTaskProcessor) GetTaskType() string {
	return string(model.TaskTypeDaily)
}

// processDailyCheckIn handles daily check-in task
func (p *DailyTaskProcessor) processDailyCheckIn(ctx context.Context, userID uuid.UUID, task *model.ActivityTask) error {
	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete daily check-in: %w", err)
	}

	global.GVA_LOG.Info("Daily check-in completed", zap.String("user_id", userID.String()))
	return nil
}

// processMemeTrade handles MEME trade completion
func (p *DailyTaskProcessor) processMemeTrade(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Check if task can be completed based on frequency
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// For daily tasks, check if already completed today
	if task.Frequency == "DAILY" && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("MEME trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data - now we verify based on actual transaction records
	// This will be called when affiliate_transactions table has new MEME trade records
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid MEME trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "MEME" {
		return fmt.Errorf("invalid trade type for MEME task")
	}

	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete MEME trade task: %w", err)
	}

	global.GVA_LOG.Info("MEME trade task completed",
		zap.String("user_id", userID.String()),
		zap.Float64("volume", volume))
	return nil
}

// processPerpetualTrade handles perpetual trade completion
func (p *DailyTaskProcessor) processPerpetualTrade(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Check if task can be completed based on frequency
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// For daily tasks, check if already completed today
	if task.Frequency == "DAILY" && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("Perpetual trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data - now we verify based on actual transaction records
	// This will be called when hyper_liquid_transactions table has new filled records
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid perpetual trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "PERPETUAL" {
		return fmt.Errorf("invalid trade type for perpetual task")
	}

	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete perpetual trade task: %w", err)
	}

	global.GVA_LOG.Info("Perpetual trade task completed",
		zap.String("user_id", userID.String()),
		zap.Float64("volume", volume))
	return nil
}

// processMarketCheck handles market condition check
func (p *DailyTaskProcessor) processMarketCheck(ctx context.Context, userID uuid.UUID, task *model.ActivityTask) error {
	// Update user activity
	if err := p.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete market check task: %w", err)
	}

	global.GVA_LOG.Info("Market check task completed", zap.String("user_id", userID.String()))
	return nil
}

// processConsecutiveCheckIn handles consecutive check-in tasks
func (p *DailyTaskProcessor) processConsecutiveCheckIn(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Get current progress
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if user checked in today
	today := time.Now().Truncate(24 * time.Hour)
	lastCheckIn := time.Time{}
	if progress.LastCompletedAt != nil {
		lastCheckIn = progress.LastCompletedAt.Truncate(24 * time.Hour)
	}

	// If last check-in was yesterday, increment streak
	yesterday := today.Add(-24 * time.Hour)
	if lastCheckIn.Equal(yesterday) {
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to update streak: %w", err)
		}
	} else if !lastCheckIn.Equal(today) {
		// Reset streak if gap > 1 day
		if err := p.service.ResetStreak(ctx, userID, task.ID); err != nil {
			return fmt.Errorf("failed to reset streak: %w", err)
		}
		// Start new streak
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start new streak: %w", err)
		}
	}

	// Update progress
	if err := p.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment progress: %w", err)
	}

	// Check if milestone reached (3, 7, or 30 days)
	updatedProgress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get updated progress: %w", err)
	}

	milestones := []int{3, 7, 30}
	for _, milestone := range milestones {
		if updatedProgress.StreakCount == milestone {
			// Award bonus points for milestone
			bonusPoints := map[int]int{3: 50, 7: 200, 30: 1000}
			if err := p.service.AddPoints(ctx, userID, bonusPoints[milestone], fmt.Sprintf("consecutive_checkin_%d", milestone)); err != nil {
				global.GVA_LOG.Error("Failed to award milestone bonus", zap.Error(err))
			}
			global.GVA_LOG.Info("Consecutive check-in milestone reached",
				zap.String("user_id", userID.String()),
				zap.Int("milestone", milestone),
				zap.Int("bonus_points", bonusPoints[milestone]))
			break
		}
	}

	return nil
}

// processConsecutiveTradingDays handles consecutive trading days tasks
func (p *DailyTaskProcessor) processConsecutiveTradingDays(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Get current progress
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if user traded today
	today := time.Now().Truncate(24 * time.Hour)
	lastTradingDay := time.Time{}
	if progress.LastCompletedAt != nil {
		lastTradingDay = progress.LastCompletedAt.Truncate(24 * time.Hour)
	}

	// If last trading day was yesterday, increment streak
	yesterday := today.Add(-24 * time.Hour)
	if lastTradingDay.Equal(yesterday) {
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to update streak: %w", err)
		}
	} else if !lastTradingDay.Equal(today) {
		// Reset streak if gap > 1 day
		if err := p.service.ResetStreak(ctx, userID, task.ID); err != nil {
			return fmt.Errorf("failed to reset streak: %w", err)
		}
		// Start new streak
		if err := p.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start new streak: %w", err)
		}
	}

	// Update progress
	if err := p.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment progress: %w", err)
	}

	// Check if milestone reached (3, 7, 15, or 30 days)
	updatedProgress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get updated progress: %w", err)
	}

	milestones := []int{3, 7, 15, 30}
	for _, milestone := range milestones {
		if updatedProgress.StreakCount == milestone {
			// Award bonus points for milestone
			bonusPoints := map[int]int{3: 50, 7: 200, 15: 1000, 30: 2000}
			if err := p.service.AddPoints(ctx, userID, bonusPoints[milestone], fmt.Sprintf("consecutive_trading_%d", milestone)); err != nil {
				global.GVA_LOG.Error("Failed to award milestone bonus", zap.Error(err))
			}
			global.GVA_LOG.Info("Consecutive trading milestone reached",
				zap.String("user_id", userID.String()),
				zap.Int("milestone", milestone),
				zap.Int("bonus_points", bonusPoints[milestone]))
			break
		}
	}

	return nil
}

// CommunityTaskProcessor handles community tasks
type CommunityTaskProcessor struct {
	service ActivityCashbackServiceInterface
}

// NewCommunityTaskProcessor creates a new CommunityTaskProcessor
func NewCommunityTaskProcessor(service ActivityCashbackServiceInterface) TaskProcessor {
	return &CommunityTaskProcessor{
		service: service,
	}
}

// ProcessTask processes community tasks
func (p *CommunityTaskProcessor) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	switch task.Name {
	case "Follow on X (Twitter)", "Follow Twitter":
		return p.processTwitterFollow(ctx, userID, task, data)
	case "Retweet a post", "Retweet Post":
		return p.processTwitterRetweet(ctx, userID, task, data)
	case "Like a post", "Like Post":
		return p.processTwitterLike(ctx, userID, task, data)
	case "Join Telegram":
		return p.processTelegramJoin(ctx, userID, task, data)
	case "Invite friends", "Invite Friends":
		return p.processInviteFriends(ctx, userID, task, data)
	case "Share referral link", "Share Referral Link":
		return p.processShareReferralLink(ctx, userID, task, data)
	default:
		return fmt.Errorf("unknown community task: %s", task.Name)
	}
}

// CanProcess checks if this processor can handle the task
func (p *CommunityTaskProcessor) CanProcess(task *model.ActivityTask) bool {
	return task.TaskType == model.TaskTypeOneTime || task.TaskType == model.TaskTypeUnlimited || task.TaskType == model.TaskTypeManualUpdate
}

// GetTaskType returns the task type this processor handles
func (p *CommunityTaskProcessor) GetTaskType() string {
	return "COMMUNITY"
}

// processTwitterFollow handles Twitter follow task
func (p *CommunityTaskProcessor) processTwitterFollow(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// For now, we trust the frontend verification
	// In production, this would verify with Twitter API

	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Twitter follow task: %w", err)
	}

	global.GVA_LOG.Info("Twitter follow task completed", zap.String("user_id", userID.String()))
	return nil
}

// processTwitterRetweet handles Twitter retweet task
func (p *CommunityTaskProcessor) processTwitterRetweet(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Manual verification task - requires admin approval
	// For now, mark as completed

	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Twitter retweet task: %w", err)
	}

	global.GVA_LOG.Info("Twitter retweet task completed", zap.String("user_id", userID.String()))
	return nil
}

// processTwitterLike handles Twitter like task
func (p *CommunityTaskProcessor) processTwitterLike(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Manual verification task - requires admin approval
	// For now, mark as completed

	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Twitter like task: %w", err)
	}

	global.GVA_LOG.Info("Twitter like task completed", zap.String("user_id", userID.String()))
	return nil
}

// processTelegramJoin handles Telegram join task
func (p *CommunityTaskProcessor) processTelegramJoin(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// For now, we trust the frontend verification
	// In production, this would verify with Telegram API

	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Telegram join task: %w", err)
	}

	global.GVA_LOG.Info("Telegram join task completed", zap.String("user_id", userID.String()))
	return nil
}

// processInviteFriends handles invite friends task
func (p *CommunityTaskProcessor) processInviteFriends(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Verify friend registration
	friendID, ok := data["friend_id"].(string)
	if !ok {
		return fmt.Errorf("friend ID not provided")
	}

	friendUUID, err := uuid.Parse(friendID)
	if err != nil {
		return fmt.Errorf("invalid friend ID: %w", err)
	}

	// Verify that the friend was actually referred by this user
	// This would typically check the referral system

	// Award points for successful referral
	if err := p.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment invite progress: %w", err)
	}

	global.GVA_LOG.Info("Friend invite task completed",
		zap.String("user_id", userID.String()),
		zap.String("friend_id", friendUUID.String()))
	return nil
}

// processShareReferralLink handles share referral link task
func (p *CommunityTaskProcessor) processShareReferralLink(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// For daily sharing task
	if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete share referral link task: %w", err)
	}

	global.GVA_LOG.Info("Share referral link task completed", zap.String("user_id", userID.String()))
	return nil
}

// TradingTaskProcessor handles trading tasks
type TradingTaskProcessor struct {
	service ActivityCashbackServiceInterface
}

// NewTradingTaskProcessor creates a new TradingTaskProcessor
func NewTradingTaskProcessor(service ActivityCashbackServiceInterface) TaskProcessor {
	return &TradingTaskProcessor{
		service: service,
	}
}

// ProcessTask processes trading tasks
func (p *TradingTaskProcessor) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	switch task.Name {
	case "Trading Points":
		return p.processTradingPoints(ctx, userID, task, data)
	case "Cumulative trading $10,000", "Accumulated Trading $10,000":
		return p.processAccumulatedTrading(ctx, userID, task, data, 10000)
	case "Cumulative trading $50,000", "Accumulated Trading $50,000":
		return p.processAccumulatedTrading(ctx, userID, task, data, 50000)
	case "Cumulative trading $100,000", "Accumulated Trading $100,000":
		return p.processAccumulatedTrading(ctx, userID, task, data, 100000)
	case "Cumulative trading $500,000", "Accumulated Trading $500,000":
		return p.processAccumulatedTrading(ctx, userID, task, data, 500000)
	default:
		return fmt.Errorf("unknown trading task: %s", task.Name)
	}
}

// CanProcess checks if this processor can handle the task
func (p *TradingTaskProcessor) CanProcess(task *model.ActivityTask) bool {
	return task.TaskType == model.TaskTypeProgressive || task.TaskType == model.TaskTypeUnlimited
}

// GetTaskType returns the task type this processor handles
func (p *TradingTaskProcessor) GetTaskType() string {
	return "TRADING"
}

// processTradingPoints handles trading points based on volume
func (p *TradingTaskProcessor) processTradingPoints(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid trading volume")
	}

	// Get trade type to apply different weights
	tradeType, ok := data["trade_type"].(string)
	if !ok {
		tradeType = "MEME" // Default to MEME if not specified
	}

	// Apply weight based on trade type
	// Derivatives (futures) have leverage, so we apply a weight factor
	weightedVolume := volume
	switch tradeType {
	case "PERPETUAL", "DERIVATIVES":
		// Apply 0.5 weight for derivatives due to leverage
		weightedVolume = volume * 0.5
	case "MEME":
		// No weight adjustment for spot trading
		weightedVolume = volume
	}

	// Calculate points based on weighted volume tiers
	var points int
	switch {
	case weightedVolume >= 10000:
		points = 40
	case weightedVolume >= 3000:
		points = 25
	case weightedVolume >= 500:
		points = 12
	case weightedVolume >= 100:
		points = 5
	case weightedVolume >= 1:
		points = 1
	default:
		points = 0
	}

	if points > 0 {
		// Award points directly (not through task completion)
		if err := p.service.AddPoints(ctx, userID, points, fmt.Sprintf("trading_volume_%.2f", volume)); err != nil {
			return fmt.Errorf("failed to award trading points: %w", err)
		}

		// Update progress
		if err := p.service.IncrementProgress(ctx, userID, task.ID, points); err != nil {
			return fmt.Errorf("failed to update trading points progress: %w", err)
		}

		global.GVA_LOG.Info("Trading points awarded",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("original_volume", volume),
			zap.Float64("weighted_volume", weightedVolume),
			zap.Int("points", points))
	}

	return nil
}

// processAccumulatedTrading handles accumulated trading milestones
func (p *TradingTaskProcessor) processAccumulatedTrading(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}, milestone float64) error {
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid trading volume")
	}

	// Get trade type to apply different weights
	tradeType, ok := data["trade_type"].(string)
	if !ok {
		tradeType = "MEME" // Default to MEME if not specified
	}

	// Apply weight based on trade type for accumulated volume
	weightedVolume := volume
	switch tradeType {
	case "PERPETUAL", "DERIVATIVES":
		// Apply 0.5 weight for derivatives due to leverage
		weightedVolume = volume * 0.5
	case "MEME":
		// No weight adjustment for spot trading
		weightedVolume = volume
	}

	// Get current progress
	progress, err := p.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if already completed
	if progress.IsCompleted() {
		return nil // Already completed
	}

	// Update accumulated volume using weighted volume
	newTotal := float64(progress.ProgressValue) + weightedVolume
	if err := p.service.SetProgress(ctx, userID, task.ID, int(newTotal)); err != nil {
		return fmt.Errorf("failed to update accumulated trading progress: %w", err)
	}

	// Check if milestone reached
	if newTotal >= milestone {
		if err := p.service.CompleteProgress(ctx, userID, task.ID); err != nil {
			return fmt.Errorf("failed to complete accumulated trading task: %w", err)
		}

		global.GVA_LOG.Info("Accumulated trading milestone reached",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("milestone", milestone),
			zap.Float64("weighted_volume_added", weightedVolume),
			zap.Float64("total_weighted_volume", newTotal),
			zap.Int("points", task.Points))
	}

	return nil
}

// TaskProcessorManager manages all task processors
type TaskProcessorManager struct {
	processors []TaskProcessor
	service    ActivityCashbackServiceInterface
	registry   *TaskRegistry // Add task registry
}

// NewTaskProcessorManager creates a new TaskProcessorManager
func NewTaskProcessorManager(service ActivityCashbackServiceInterface) *TaskProcessorManager {
	registry := NewTaskRegistry(service)
	return &TaskProcessorManager{
		processors: []TaskProcessor{
			NewDailyTaskProcessor(service),
			NewCommunityTaskProcessor(service),
			NewTradingTaskProcessor(service),
		},
		service:  service,
		registry: registry,
	}
}

// ProcessTask processes a task using the appropriate processor
func (m *TaskProcessorManager) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Try new registry-based approach first
	if err := m.registry.ProcessTask(ctx, userID, task, data); err == nil {
		return nil
	}

	// Fallback to old processor-based approach for backward compatibility
	for _, processor := range m.processors {
		if processor.CanProcess(task) {
			return processor.ProcessTask(ctx, userID, task, data)
		}
	}
	return fmt.Errorf("no processor found for task type: %s", task.TaskType)
}

// ProcessTaskByName processes a task by name and category
func (m *TaskProcessorManager) ProcessTaskByName(ctx context.Context, userID uuid.UUID, taskName, categoryName string, data map[string]interface{}) error {
	// Try new registry-based approach first
	if err := m.registry.ProcessTaskByName(ctx, userID, taskName, categoryName, data); err == nil {
		return nil
	}

	// Fallback to old approach for backward compatibility
	// Get tasks by category
	tasks, err := m.service.GetTasksByCategory(ctx, categoryName)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Find the specific task
	var targetTask *model.ActivityTask
	for _, task := range tasks {
		if task.Name == taskName {
			targetTask = &task
			break
		}
	}

	if targetTask == nil {
		return fmt.Errorf("task not found: %s in category %s", taskName, categoryName)
	}

	return m.ProcessTask(ctx, userID, targetTask, data)
}

// ProcessTradingEvent processes trading events and updates relevant tasks
func (m *TaskProcessorManager) ProcessTradingEvent(ctx context.Context, userID uuid.UUID, tradeData map[string]interface{}) error {
	tradeType, ok := tradeData["trade_type"].(string)
	if !ok {
		return fmt.Errorf("trade type not specified")
	}

	volume, ok := tradeData["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid trade volume")
	}

	// Process daily trading tasks
	switch tradeType {
	case "MEME":
		// Try new name first, fallback to old name for backward compatibility
		if err := m.ProcessTaskByName(ctx, userID, "Complete one meme trade", "daily", tradeData); err != nil {
			if err := m.ProcessTaskByName(ctx, userID, "Complete 1 MEME Trade", "daily", tradeData); err != nil {
				global.GVA_LOG.Error("Failed to process MEME trade task", zap.Error(err))
			}
		}
	case "PERPETUAL":
		// Try new name first, fallback to old name for backward compatibility
		if err := m.ProcessTaskByName(ctx, userID, "Complete one derivatives trade", "daily", tradeData); err != nil {
			if err := m.ProcessTaskByName(ctx, userID, "Complete 1 Perpetual Trade", "daily", tradeData); err != nil {
				global.GVA_LOG.Error("Failed to process perpetual trade task", zap.Error(err))
			}
		}
	}

	// Process trading points task
	if err := m.ProcessTaskByName(ctx, userID, "Trading Points", "trading", tradeData); err != nil {
		global.GVA_LOG.Error("Failed to process trading points task", zap.Error(err))
	}

	// Process accumulated trading tasks - try new names first, fallback to old names
	accumulatedTasks := []struct {
		newName string
		oldName string
	}{
		{"Cumulative trading $10,000", "Accumulated Trading $10,000"},
		{"Cumulative trading $50,000", "Accumulated Trading $50,000"},
		{"Cumulative trading $100,000", "Accumulated Trading $100,000"},
		{"Cumulative trading $500,000", "Accumulated Trading $500,000"},
	}

	for _, task := range accumulatedTasks {
		// Try new name first, fallback to old name for backward compatibility
		if err := m.ProcessTaskByName(ctx, userID, task.newName, "trading", tradeData); err != nil {
			if err := m.ProcessTaskByName(ctx, userID, task.oldName, "trading", tradeData); err != nil {
				global.GVA_LOG.Error("Failed to process accumulated trading task",
					zap.Error(err), zap.String("task", task.newName))
			}
		}
	}

	// Process consecutive trading days task
	if err := m.ProcessTaskByName(ctx, userID, "Trade for 3/7/15/30 consecutive days", "daily", tradeData); err != nil {
		global.GVA_LOG.Error("Failed to process consecutive trading days task", zap.Error(err))
	}

	return nil
}
