# Task System Migration Guide

## Overview

This guide provides step-by-step instructions for migrating from the old hardcoded task name system to the new Task Identifier architecture.

## Pre-Migration Checklist

### 1. Environment Preparation
- [ ] Backup production database
- [ ] Test migration on staging environment
- [ ] Verify all dependencies are updated
- [ ] Ensure monitoring is in place

### 2. Code Review
- [ ] All new files are properly integrated
- [ ] GraphQL schema is updated
- [ ] Tests are passing
- [ ] Documentation is complete

### 3. Database Preparation
- [ ] Review migration script
- [ ] Check for any custom task names not in mapping
- [ ] Verify database permissions

## Migration Steps

### Step 1: Deploy Code Changes

```bash
# 1. Deploy the new code with backward compatibility
git checkout main
git pull origin main
make build-local
make test

# 2. Deploy to staging first
kubectl apply -f k8s/staging/
kubectl rollout status deployment/xbit-agent -n staging

# 3. Run integration tests
make test-integration

# 4. Deploy to production
kubectl apply -f k8s/production/
kubectl rollout status deployment/xbit-agent -n production
```

### Step 2: Run Database Migration

```sql
-- Execute the migration script
\i migrations/20241228_add_task_identifier.sql

-- Verify the migration
SELECT 
    name, 
    task_identifier,
    CASE 
        WHEN task_identifier IS NOT NULL THEN 'MIGRATED'
        ELSE 'PENDING'
    END as migration_status
FROM activity_tasks 
ORDER BY created_at;

-- Check migration statistics
SELECT 
    COUNT(*) as total_tasks,
    COUNT(task_identifier) as migrated_tasks,
    COUNT(*) - COUNT(task_identifier) as pending_tasks,
    ROUND(COUNT(task_identifier) * 100.0 / COUNT(*), 2) as migration_percentage
FROM activity_tasks;
```

### Step 3: Monitor System Health

```bash
# Check application logs for any errors
kubectl logs -f deployment/xbit-agent -n production | grep -i "task\|error"

# Monitor task processing metrics
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://api.xbit-dex.com/admin/activity/stats

# Check for legacy fallback usage
kubectl logs deployment/xbit-agent -n production | grep "Using legacy processor"
```

### Step 4: Gradual Task Migration

```go
// Use admin API to migrate specific tasks
func migrateTasksGradually() {
    tasksToMigrate := []struct {
        name       string
        identifier string
    }{
        {"Daily Check-in", "DAILY_CHECKIN"},
        {"Complete one meme trade", "MEME_TRADE_DAILY"},
        {"Complete one derivatives trade", "PERPETUAL_TRADE_DAILY"},
        // Add more tasks as needed
    }

    for _, task := range tasksToMigrate {
        if err := migrateTask(task.name, task.identifier); err != nil {
            log.Error("Failed to migrate task", 
                zap.String("name", task.name),
                zap.Error(err))
        } else {
            log.Info("Task migrated successfully", 
                zap.String("name", task.name),
                zap.String("identifier", task.identifier))
        }
    }
}
```

## Monitoring and Validation

### Key Metrics to Track

1. **Migration Progress**
```sql
-- Track migration progress
SELECT 
    category.name as category,
    COUNT(*) as total_tasks,
    COUNT(task.task_identifier) as migrated_tasks,
    ROUND(COUNT(task.task_identifier) * 100.0 / COUNT(*), 2) as progress_percentage
FROM activity_tasks task
JOIN task_categories category ON task.category_id = category.id
GROUP BY category.name
ORDER BY progress_percentage DESC;
```

2. **System Performance**
```bash
# Monitor response times
curl -w "@curl-format.txt" -s -o /dev/null \
     -H "Authorization: Bearer $TOKEN" \
     -X POST \
     -d '{"query": "query { userDashboard(userId: \"test\") { taskCenter { categories { tasks { taskIdentifier } } } } }"}' \
     https://api.xbit-dex.com/graphql
```

3. **Error Rates**
```bash
# Check error logs
kubectl logs deployment/xbit-agent -n production --since=1h | \
    grep -E "(error|ERROR|failed|FAILED)" | \
    grep -i task | \
    wc -l
```

### Health Check Queries

```graphql
# Verify task system health
query SystemHealthCheck {
  systemMetrics {
    taskProcessingStats {
      registrySuccessRate
      legacyFallbackRate
      averageProcessingTime
      errorRate
    }
    compatibilityStats {
      registryHandlers
      legacyProcessors
      identifierMappings
    }
  }
}
```

## Rollback Procedures

### Emergency Rollback

If critical issues are detected:

```bash
# 1. Rollback to previous deployment
kubectl rollout undo deployment/xbit-agent -n production

# 2. Verify rollback
kubectl rollout status deployment/xbit-agent -n production

# 3. Check system health
curl https://api.xbit-dex.com/health
```

### Partial Rollback

If only specific tasks are problematic:

```sql
-- Temporarily disable problematic tasks
UPDATE activity_tasks 
SET is_active = false 
WHERE task_identifier IN ('PROBLEMATIC_TASK_1', 'PROBLEMATIC_TASK_2');

-- Or remove task identifiers to force legacy processing
UPDATE activity_tasks 
SET task_identifier = NULL 
WHERE task_identifier IN ('PROBLEMATIC_TASK_1', 'PROBLEMATIC_TASK_2');
```

## Post-Migration Tasks

### 1. Performance Optimization

```sql
-- Add indexes for better performance
CREATE INDEX CONCURRENTLY idx_activity_tasks_identifier_active 
ON activity_tasks(task_identifier) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_user_task_progress_user_task 
ON user_task_progress(user_id, task_id);
```

### 2. Clean Up Legacy Code

After successful migration (2-4 weeks):

```go
// Remove legacy processors (gradually)
// 1. First, log warnings for legacy usage
// 2. Then disable legacy processors for non-critical tasks
// 3. Finally, remove legacy code entirely

// Example: Mark legacy processors as deprecated
func (p *LegacyTaskProcessor) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
    log.Warn("DEPRECATED: Using legacy task processor", 
        zap.String("task_name", task.Name),
        zap.String("user_id", userID.String()))
    
    // Continue with legacy processing...
}
```

### 3. Update Documentation

- [ ] Update API documentation
- [ ] Update frontend integration guides
- [ ] Update monitoring runbooks
- [ ] Create troubleshooting guides

## Troubleshooting Common Issues

### Issue 1: Task Not Processing

**Symptoms:**
- Task completion fails silently
- No error logs
- User progress not updated

**Diagnosis:**
```sql
-- Check if task has identifier
SELECT id, name, task_identifier, is_active 
FROM activity_tasks 
WHERE name = 'Task Name';

-- Check if mapping exists
SELECT * FROM unnest(array[
    'Task Name 1',
    'Task Name 2'
]) AS task_name
WHERE task_name = ANY(SELECT key FROM json_each_text('{"Complete one meme trade": "MEME_TRADE_DAILY"}'::json));
```

**Resolution:**
1. Add missing task identifier
2. Update legacy name mapping
3. Restart application

### Issue 2: High Legacy Fallback Rate

**Symptoms:**
- Many "Using legacy processor" log messages
- Slower task processing
- Inconsistent behavior

**Diagnosis:**
```bash
# Check fallback rate
kubectl logs deployment/xbit-agent -n production --since=1h | \
    grep "Using legacy processor" | \
    wc -l
```

**Resolution:**
1. Identify tasks without identifiers
2. Run migration for missing tasks
3. Update task seeder

### Issue 3: Performance Degradation

**Symptoms:**
- Slower API responses
- Increased database load
- Timeout errors

**Diagnosis:**
```sql
-- Check for missing indexes
EXPLAIN ANALYZE 
SELECT * FROM activity_tasks 
WHERE task_identifier = 'DAILY_CHECKIN';

-- Check query performance
SELECT 
    query,
    mean_time,
    calls
FROM pg_stat_statements 
WHERE query LIKE '%activity_tasks%'
ORDER BY mean_time DESC;
```

**Resolution:**
1. Add missing database indexes
2. Optimize query patterns
3. Enable query caching

## Best Practices

### 1. Task Identifier Naming

```go
// ✅ Good: Clear, consistent naming
const (
    TaskIDDailyCheckin        TaskIdentifier = "DAILY_CHECKIN"
    TaskIDMemeTradeDaily      TaskIdentifier = "MEME_TRADE_DAILY"
    TaskIDPerpetualTradeDaily TaskIdentifier = "PERPETUAL_TRADE_DAILY"
)

// ❌ Bad: Inconsistent, unclear naming
const (
    TaskID1 TaskIdentifier = "task1"
    TaskID2 TaskIdentifier = "MEME_TRADE"
    TaskID3 TaskIdentifier = "derivatives_trade_daily"
)
```

### 2. Error Handling

```go
// ✅ Good: Comprehensive error handling
func (tcl *TaskCompatibilityLayer) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
    // Try new system first
    if err := tcl.registry.ProcessTask(ctx, userID, task, data); err == nil {
        return nil
    } else {
        log.Warn("Registry processing failed, falling back to legacy", 
            zap.Error(err),
            zap.String("task_name", task.Name))
    }
    
    // Fallback to legacy with proper error handling
    if err := tcl.legacyProcessors.ProcessTask(ctx, userID, task, data); err != nil {
        log.Error("Both registry and legacy processing failed", 
            zap.Error(err),
            zap.String("task_name", task.Name))
        return fmt.Errorf("task processing failed: %w", err)
    }
    
    return nil
}
```

### 3. Monitoring

```go
// ✅ Good: Comprehensive monitoring
func (tcl *TaskCompatibilityLayer) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        metrics.TaskProcessingDuration.Observe(duration.Seconds())
    }()
    
    if task.TaskIdentifier != nil {
        metrics.RegistryProcessingAttempts.Inc()
        if err := tcl.registry.ProcessTask(ctx, userID, task, data); err == nil {
            metrics.RegistryProcessingSuccess.Inc()
            return nil
        }
        metrics.RegistryProcessingFailures.Inc()
    }
    
    metrics.LegacyFallbackAttempts.Inc()
    // Continue with legacy processing...
}
```

## Success Criteria

The migration is considered successful when:

- [ ] **95%+ tasks** are using the new identifier system
- [ ] **Legacy fallback rate** is below 5%
- [ ] **Task processing performance** is improved or maintained
- [ ] **Zero critical errors** related to task processing
- [ ] **All tests passing** including integration tests
- [ ] **Monitoring shows** healthy system metrics

## Timeline

| Phase | Duration | Activities |
|-------|----------|------------|
| **Preparation** | 1 week | Code review, testing, documentation |
| **Deployment** | 1 day | Deploy code, run migration |
| **Monitoring** | 1 week | Monitor system health, fix issues |
| **Optimization** | 2 weeks | Performance tuning, gradual migration |
| **Cleanup** | 2 weeks | Remove legacy code, final documentation |

**Total Timeline: ~6 weeks**
