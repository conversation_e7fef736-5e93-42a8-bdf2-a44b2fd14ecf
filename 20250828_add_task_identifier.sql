-- Migration: Add task_identifier field to activity_tasks table
-- Date: 2024-12-28
-- Description: Add unique task identifier field for better task identification and processing

-- Add task_identifier column to activity_tasks table
ALTER TABLE activity_tasks 
ADD COLUMN task_identifier VARCHAR(50);

-- <PERSON>reate index on task_identifier for better performance
CREATE INDEX idx_activity_tasks_task_identifier ON activity_tasks(task_identifier);

-- Update existing tasks with their corresponding identifiers
-- Daily Tasks
UPDATE activity_tasks SET task_identifier = 'DAILY_CHECKIN' 
WHERE name = 'Daily Check-in';

UPDATE activity_tasks SET task_identifier = 'MEME_TRADE_DAILY' 
WHERE name IN ('Complete one meme trade', 'Complete 1 MEME Trade');

UPDATE activity_tasks SET task_identifier = 'PERPETUAL_TRADE_DAILY' 
WHERE name IN ('Complete one derivatives trade', 'Complete 1 Perpetual Trade');

UPDATE activity_tasks SET task_identifier = 'MARKET_PAGE_VIEW' 
WHERE name IN ('View market page', 'Check Market Conditions');

UPDATE activity_tasks SET task_identifier = 'CONSECUTIVE_CHECKIN' 
WHERE name = 'Consecutive Check-in (3/7/30 days)';

UPDATE activity_tasks SET task_identifier = 'CONSECUTIVE_TRADING_DAYS' 
WHERE name = 'Trade for 3/7/15/30 consecutive days';

-- Community Tasks
UPDATE activity_tasks SET task_identifier = 'TWITTER_FOLLOW' 
WHERE name IN ('Follow on X (Twitter)', 'Follow Twitter');

UPDATE activity_tasks SET task_identifier = 'TWITTER_RETWEET' 
WHERE name IN ('Retweet a post', 'Retweet Post');

UPDATE activity_tasks SET task_identifier = 'TWITTER_LIKE' 
WHERE name IN ('Like a post', 'Like Post');

UPDATE activity_tasks SET task_identifier = 'TELEGRAM_JOIN' 
WHERE name = 'Join Telegram';

UPDATE activity_tasks SET task_identifier = 'INVITE_FRIENDS' 
WHERE name IN ('Invite friends', 'Invite Friends');

UPDATE activity_tasks SET task_identifier = 'SHARE_REFERRAL' 
WHERE name IN ('Share referral link', 'Share Referral Link');

-- Trading Tasks
UPDATE activity_tasks SET task_identifier = 'TRADING_POINTS' 
WHERE name = 'Trading Points';

UPDATE activity_tasks SET task_identifier = 'ACCUMULATED_TRADING_10K' 
WHERE name IN ('Cumulative trading $10,000', 'Accumulated Trading $10,000');

UPDATE activity_tasks SET task_identifier = 'ACCUMULATED_TRADING_50K' 
WHERE name IN ('Cumulative trading $50,000', 'Accumulated Trading $50,000');

UPDATE activity_tasks SET task_identifier = 'ACCUMULATED_TRADING_100K' 
WHERE name IN ('Cumulative trading $100,000', 'Accumulated Trading $100,000');

UPDATE activity_tasks SET task_identifier = 'ACCUMULATED_TRADING_500K' 
WHERE name IN ('Cumulative trading $500,000', 'Accumulated Trading $500,000');

-- Add constraint to ensure task_identifier is unique when not null
ALTER TABLE activity_tasks 
ADD CONSTRAINT unique_task_identifier UNIQUE (task_identifier);

-- Add comment to the column
COMMENT ON COLUMN activity_tasks.task_identifier IS 'Unique identifier for task type, used for programmatic task identification';
