package model

// TaskIdentifier represents unique identifiers for tasks
type TaskIdentifier string

// Daily Task Identifiers
const (
	TaskIDDailyCheckin           TaskIdentifier = "DAILY_CHECKIN"
	TaskIDMemeTradeDaily         TaskIdentifier = "MEME_TRADE_DAILY"
	TaskIDPerpetualTradeDaily    TaskIdentifier = "PERPETUAL_TRADE_DAILY"
	TaskIDMarketPageView         TaskIdentifier = "MARKET_PAGE_VIEW"
	TaskIDConsecutiveCheckin     TaskIdentifier = "CONSECUTIVE_CHECKIN"
	TaskIDConsecutiveTradingDays TaskIdentifier = "CONSECUTIVE_TRADING_DAYS"
)

// Community Task Identifiers
const (
	TaskIDTwitterFollow    TaskIdentifier = "TWITTER_FOLLOW"
	TaskIDTwitterRetweet   TaskIdentifier = "TWITTER_RETWEET"
	TaskIDTwitterLike      TaskIdentifier = "TWITTER_LIKE"
	TaskIDTelegramJoin     TaskIdentifier = "TELEGRAM_JOIN"
	TaskIDInviteFriends    TaskIdentifier = "INVITE_FRIENDS"
	TaskIDShareReferral    TaskIdentifier = "SHARE_REFERRAL"
)

// Trading Task Identifiers
const (
	TaskIDTradingPoints        TaskIdentifier = "TRADING_POINTS"
	TaskIDAccumulatedTrading10K  TaskIdentifier = "ACCUMULATED_TRADING_10K"
	TaskIDAccumulatedTrading50K  TaskIdentifier = "ACCUMULATED_TRADING_50K"
	TaskIDAccumulatedTrading100K TaskIdentifier = "ACCUMULATED_TRADING_100K"
	TaskIDAccumulatedTrading500K TaskIdentifier = "ACCUMULATED_TRADING_500K"
)

// TaskIdentifierMap maps task identifiers to their display names
var TaskIdentifierMap = map[TaskIdentifier]string{
	// Daily Tasks
	TaskIDDailyCheckin:           "Daily Check-in",
	TaskIDMemeTradeDaily:         "Complete one meme trade",
	TaskIDPerpetualTradeDaily:    "Complete one derivatives trade",
	TaskIDMarketPageView:         "View market page",
	TaskIDConsecutiveCheckin:     "Consecutive Check-in (3/7/30 days)",
	TaskIDConsecutiveTradingDays: "Trade for 3/7/15/30 consecutive days",
	
	// Community Tasks
	TaskIDTwitterFollow:  "Follow on X (Twitter)",
	TaskIDTwitterRetweet: "Retweet a post",
	TaskIDTwitterLike:    "Like a post",
	TaskIDTelegramJoin:   "Join Telegram",
	TaskIDInviteFriends:  "Invite friends",
	TaskIDShareReferral:  "Share referral link",
	
	// Trading Tasks
	TaskIDTradingPoints:        "Trading Points",
	TaskIDAccumulatedTrading10K:  "Cumulative trading $10,000",
	TaskIDAccumulatedTrading50K:  "Cumulative trading $50,000",
	TaskIDAccumulatedTrading100K: "Cumulative trading $100,000",
	TaskIDAccumulatedTrading500K: "Cumulative trading $500,000",
}

// LegacyTaskNameMap maps old task names to new identifiers for backward compatibility
var LegacyTaskNameMap = map[string]TaskIdentifier{
	// Daily Tasks - Old names
	"Daily Check-in":                    TaskIDDailyCheckin,
	"Complete one meme trade":           TaskIDMemeTradeDaily,
	"Complete 1 MEME Trade":             TaskIDMemeTradeDaily,
	"Complete one derivatives trade":    TaskIDPerpetualTradeDaily,
	"Complete 1 Perpetual Trade":        TaskIDPerpetualTradeDaily,
	"View market page":                  TaskIDMarketPageView,
	"Check Market Conditions":           TaskIDMarketPageView,
	"Consecutive Check-in (3/7/30 days)": TaskIDConsecutiveCheckin,
	"Trade for 3/7/15/30 consecutive days": TaskIDConsecutiveTradingDays,
	
	// Community Tasks - Old names
	"Follow on X (Twitter)": TaskIDTwitterFollow,
	"Follow Twitter":        TaskIDTwitterFollow,
	"Retweet a post":        TaskIDTwitterRetweet,
	"Retweet Post":          TaskIDTwitterRetweet,
	"Like a post":           TaskIDTwitterLike,
	"Like Post":             TaskIDTwitterLike,
	"Join Telegram":         TaskIDTelegramJoin,
	"Invite friends":        TaskIDInviteFriends,
	"Invite Friends":        TaskIDInviteFriends,
	"Share referral link":   TaskIDShareReferral,
	"Share Referral Link":   TaskIDShareReferral,
	
	// Trading Tasks - Old names
	"Trading Points":                TaskIDTradingPoints,
	"Cumulative trading $10,000":    TaskIDAccumulatedTrading10K,
	"Accumulated Trading $10,000":   TaskIDAccumulatedTrading10K,
	"Cumulative trading $50,000":    TaskIDAccumulatedTrading50K,
	"Accumulated Trading $50,000":   TaskIDAccumulatedTrading50K,
	"Cumulative trading $100,000":   TaskIDAccumulatedTrading100K,
	"Accumulated Trading $100,000":  TaskIDAccumulatedTrading100K,
	"Cumulative trading $500,000":   TaskIDAccumulatedTrading500K,
	"Accumulated Trading $500,000":  TaskIDAccumulatedTrading500K,
}

// GetTaskIdentifierByName returns the task identifier for a given task name
func GetTaskIdentifierByName(name string) (TaskIdentifier, bool) {
	identifier, exists := LegacyTaskNameMap[name]
	return identifier, exists
}

// GetTaskDisplayName returns the display name for a task identifier
func GetTaskDisplayName(identifier TaskIdentifier) string {
	if name, exists := TaskIdentifierMap[identifier]; exists {
		return name
	}
	return string(identifier)
}

// IsValidTaskIdentifier checks if a task identifier is valid
func IsValidTaskIdentifier(identifier TaskIdentifier) bool {
	_, exists := TaskIdentifierMap[identifier]
	return exists
}
