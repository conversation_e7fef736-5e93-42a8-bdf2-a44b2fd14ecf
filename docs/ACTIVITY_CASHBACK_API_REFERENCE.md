# Activity Cashback API Reference

## Overview

This document provides comprehensive API reference for the Activity Cashback system with the new Task Identifier architecture.

## GraphQL Schema

### Types

#### ActivityTask
```graphql
type ActivityTask {
  id: ID!
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: String # New field for unique identification
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  isActive: Boolean!
  startDate: Time
  endDate: Time
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
  category: TaskCategory
  userProgress: UserTaskProgress
}
```

#### UserTaskProgress
```graphql
type UserTaskProgress {
  id: ID!
  userId: ID!
  taskId: ID!
  status: TaskStatus!
  completionCount: Int!
  maxCompletions: Int
  lastCompletedAt: Time
  streak: Int!
  maxStreak: Int!
  progressValue: Int!
  targetValue: Int
  resetAt: Time
  createdAt: Time!
  updatedAt: Time!
  task: ActivityTask!
}
```

#### UserDashboard
```graphql
type UserDashboard {
  userInfo: UserTierInfo!
  taskCenter: TaskCenter!
  claimableRewards: [ClaimableReward!]!
  recentActivity: [ActivityRecord!]!
}
```

#### TaskCenter
```graphql
type TaskCenter {
  categories: [TaskCategoryWithTasks!]!
  completedToday: Int!
  pointsEarnedToday: Int!
  streakTasks: [UserTaskProgress!]!
}
```

### Queries

#### 1. Get User Dashboard
```graphql
query GetUserDashboard($userId: ID!) {
  userDashboard(userId: $userId) {
    userInfo {
      userId
      currentTier
      totalPoints
      monthlyVolume
      monthlyTrades
      nextTierRequirement {
        tier
        requiredPoints
        requiredVolume
        benefits
      }
    }
    taskCenter {
      categories {
        id
        name
        displayName
        description
        icon
        tasks {
          id
          name
          taskIdentifier
          description
          taskType
          frequency
          points
          maxCompletions
          conditions
          isActive
          progress {
            status
            completionCount
            lastCompletedAt
            streak
            progressValue
            targetValue
          }
        }
      }
      completedToday
      pointsEarnedToday
      streakTasks {
        taskId
        streak
        maxStreak
        task {
          name
          taskIdentifier
        }
      }
    }
    claimableRewards {
      id
      type
      amountUSD
      amountSOL
      status
      createdAt
    }
  }
}
```

#### 2. Get Task Categories
```graphql
query GetTaskCategories {
  taskCategories {
    id
    name
    displayName
    description
    icon
    sortOrder
    isActive
    tasks {
      id
      name
      taskIdentifier
      description
      taskType
      frequency
      points
      isActive
    }
  }
}
```

#### 3. Get User Task Progress
```graphql
query GetUserTaskProgress($userId: ID!, $categoryName: String) {
  userTaskProgress(userId: $userId, categoryName: $categoryName) {
    id
    userId
    taskId
    status
    completionCount
    maxCompletions
    lastCompletedAt
    streak
    maxStreak
    progressValue
    targetValue
    task {
      id
      name
      taskIdentifier
      description
      points
      taskType
      frequency
      category {
        name
        displayName
      }
    }
  }
}
```

#### 4. Get Task by Identifier
```graphql
query GetTaskByIdentifier($identifier: String!) {
  taskByIdentifier(identifier: $identifier) {
    id
    name
    taskIdentifier
    description
    taskType
    frequency
    points
    maxCompletions
    conditions
    isActive
    category {
      name
      displayName
    }
  }
}
```

### Mutations

#### 1. Complete Task
```graphql
mutation CompleteTask($input: CompleteTaskInput!) {
  completeTask(input: $input) {
    success
    message
    pointsAwarded
    newTier
    streakBonus
    task {
      id
      name
      taskIdentifier
      progress {
        status
        completionCount
        lastCompletedAt
        streak
      }
    }
    userInfo {
      currentTier
      totalPoints
      monthlyVolume
    }
  }
}
```

**Input Type:**
```graphql
input CompleteTaskInput {
  userId: ID!
  taskId: ID
  taskIdentifier: String # Alternative to taskId
  verificationData: JSON
  metadata: JSON
}
```

#### 2. Claim Task Reward
```graphql
mutation ClaimTaskReward($input: ClaimRewardInput!) {
  claimTaskReward(input: $input) {
    success
    message
    claimId: ID
    amountClaimed: Float
    transactionHash: String
    claim {
      id
      type
      amountUSD
      amountSOL
      status
      createdAt
    }
  }
}
```

#### 3. Reset Task Progress (Admin)
```graphql
mutation ResetTaskProgress($input: ResetTaskProgressInput!) {
  resetTaskProgress(input: $input) {
    success
    message
    affectedUsers: Int
    resetTasks: [String!]
  }
}
```

## REST API Endpoints

### Internal APIs

#### 1. Process Trading Event
```http
POST /internal/activity/trading-event
Content-Type: application/json

{
  "user_id": "uuid",
  "trade_type": "MEME|PERPETUAL",
  "volume": 1000.0,
  "timestamp": "2024-12-28T10:00:00Z",
  "metadata": {
    "symbol": "BTC/USDT",
    "side": "BUY"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Trading event processed successfully",
  "tasks_processed": [
    {
      "task_identifier": "MEME_TRADE_DAILY",
      "points_awarded": 200,
      "completed": true
    },
    {
      "task_identifier": "TRADING_POINTS",
      "points_awarded": 40,
      "completed": true
    }
  ],
  "user_stats": {
    "total_points": 1240,
    "current_tier": 2,
    "monthly_volume": 15000.0
  }
}
```

#### 2. Process Daily Check-in
```http
POST /internal/activity/daily-checkin
Content-Type: application/json

{
  "user_id": "uuid",
  "timestamp": "2024-12-28T10:00:00Z",
  "source": "web|mobile|api"
}
```

#### 3. Bulk Task Processing
```http
POST /internal/activity/bulk-process
Content-Type: application/json

{
  "tasks": [
    {
      "user_id": "uuid",
      "task_identifier": "DAILY_CHECKIN",
      "verification_data": {}
    },
    {
      "user_id": "uuid",
      "task_identifier": "MARKET_PAGE_VIEW",
      "verification_data": {
        "page": "trading",
        "duration": 30
      }
    }
  ]
}
```

### Admin APIs

#### 1. Get System Stats
```http
GET /admin/activity/stats
Authorization: Bearer <admin_token>
```

**Response:**
```json
{
  "task_processing": {
    "registry_success_rate": 0.95,
    "legacy_fallback_rate": 0.05,
    "average_processing_time_ms": 45,
    "total_tasks_processed": 10000
  },
  "compatibility": {
    "registry_handlers": 15,
    "legacy_processors": 3,
    "identifier_mappings": 25
  },
  "user_engagement": {
    "active_users_today": 500,
    "tasks_completed_today": 2500,
    "points_awarded_today": 125000
  }
}
```

#### 2. Migrate Task to New System
```http
POST /admin/activity/migrate-task
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "task_id": "uuid",
  "task_identifier": "NEW_IDENTIFIER",
  "dry_run": false
}
```

## Code Examples

### Frontend Integration

#### React Hook Example
```typescript
import { useQuery, useMutation } from '@apollo/client';
import { GET_USER_DASHBOARD, COMPLETE_TASK } from './queries';

export const useActivityDashboard = (userId: string) => {
  const { data, loading, error, refetch } = useQuery(GET_USER_DASHBOARD, {
    variables: { userId },
    pollInterval: 30000, // Refresh every 30 seconds
  });

  const [completeTask] = useMutation(COMPLETE_TASK, {
    onCompleted: () => {
      refetch(); // Refresh dashboard after task completion
    },
  });

  const handleTaskComplete = async (taskId: string, verificationData?: any) => {
    try {
      const result = await completeTask({
        variables: {
          input: {
            userId,
            taskId,
            verificationData,
          },
        },
      });
      
      return result.data.completeTask;
    } catch (error) {
      console.error('Task completion failed:', error);
      throw error;
    }
  };

  return {
    dashboard: data?.userDashboard,
    loading,
    error,
    completeTask: handleTaskComplete,
    refetch,
  };
};
```

#### Task Component Example
```typescript
interface TaskCardProps {
  task: ActivityTask;
  progress: UserTaskProgress;
  onComplete: (taskId: string) => Promise<void>;
}

export const TaskCard: React.FC<TaskCardProps> = ({ task, progress, onComplete }) => {
  const [isCompleting, setIsCompleting] = useState(false);

  const handleComplete = async () => {
    if (progress.status === 'COMPLETED') return;
    
    setIsCompleting(true);
    try {
      await onComplete(task.id);
    } catch (error) {
      console.error('Failed to complete task:', error);
    } finally {
      setIsCompleting(false);
    }
  };

  return (
    <div className="task-card">
      <div className="task-header">
        <h3>{task.name}</h3>
        <span className="task-identifier">{task.taskIdentifier}</span>
      </div>
      
      <p className="task-description">{task.description}</p>
      
      <div className="task-rewards">
        <span className="points">+{task.points} points</span>
      </div>
      
      <div className="task-progress">
        <span>Progress: {progress.completionCount}/{task.maxCompletions || '∞'}</span>
        {progress.streak > 0 && <span>Streak: {progress.streak}</span>}
      </div>
      
      <button
        onClick={handleComplete}
        disabled={progress.status === 'COMPLETED' || isCompleting}
        className={`task-button ${progress.status}`}
      >
        {isCompleting ? 'Processing...' : getButtonText(progress.status)}
      </button>
    </div>
  );
};
```

### Backend Integration

#### Service Integration Example
```go
// Trading service integration
func (s *TradingService) OnTradeExecuted(trade *Trade) error {
    // Process trading event for activity cashback
    tradeData := map[string]interface{}{
        "trade_type": trade.Type, // "MEME" or "PERPETUAL"
        "volume":     trade.Volume,
        "symbol":     trade.Symbol,
        "side":       trade.Side,
        "timestamp":  trade.ExecutedAt,
    }

    if err := s.activityService.ProcessTradingEvent(context.Background(), trade.UserID, tradeData); err != nil {
        log.Error("Failed to process trading event for activity cashback", 
            zap.Error(err), 
            zap.String("user_id", trade.UserID.String()),
            zap.String("trade_id", trade.ID.String()))
        // Don't fail the trade, just log the error
    }

    return nil
}
```

#### Middleware Example
```go
// Activity tracking middleware
func ActivityTrackingMiddleware(activityService ActivityCashbackServiceInterface) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Process request
        c.Next()

        // Track page views for market check task
        if c.Request.URL.Path == "/market" && c.Writer.Status() == 200 {
            userID := getUserIDFromContext(c)
            if userID != uuid.Nil {
                go func() {
                    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
                    defer cancel()
                    
                    data := map[string]interface{}{
                        "page":      "market",
                        "timestamp": time.Now(),
                        "user_agent": c.Request.UserAgent(),
                    }
                    
                    // This will use the new task system automatically
                    if err := activityService.ProcessTaskByName(ctx, userID, "View market page", "daily", data); err != nil {
                        log.Error("Failed to process market page view", zap.Error(err))
                    }
                }()
            }
        }
    }
}
```

## Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| `TASK_NOT_FOUND` | Task with given ID/identifier not found | Check task ID or identifier |
| `TASK_ALREADY_COMPLETED` | Task already completed for the period | Wait for reset period |
| `INVALID_VERIFICATION_DATA` | Verification data is invalid | Check required fields |
| `USER_NOT_ELIGIBLE` | User not eligible for this task | Check user tier/requirements |
| `TASK_PROCESSING_FAILED` | Internal processing error | Check logs and retry |
| `LEGACY_FALLBACK_FAILED` | Both new and legacy systems failed | Contact support |

## Rate Limits

- **Task Completion**: 10 requests per minute per user
- **Dashboard Queries**: 60 requests per minute per user  
- **Admin APIs**: 100 requests per minute per admin
- **Internal APIs**: No rate limit (trusted sources)
