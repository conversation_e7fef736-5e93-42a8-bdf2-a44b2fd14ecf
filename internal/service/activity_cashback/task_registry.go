package activity_cashback

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TaskHandler defines the interface for handling specific tasks
type TaskHandler interface {
	Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error
	GetIdentifier() model.TaskIdentifier
	GetCategory() string
}

// TaskRegistry manages task handlers
type TaskRegistry struct {
	handlers map[model.TaskIdentifier]TaskHandler
	service  ActivityCashbackServiceInterface
}

// NewTaskRegistry creates a new task registry
func NewTaskRegistry(service ActivityCashbackServiceInterface) *TaskRegistry {
	registry := &TaskRegistry{
		handlers: make(map[model.TaskIdentifier]TaskHandler),
		service:  service,
	}
	
	// Register all task handlers
	registry.registerHandlers()
	
	return registry
}

// registerHandlers registers all task handlers
func (r *TaskRegistry) registerHandlers() {
	// Daily task handlers
	r.Register<PERSON>(NewDailyCheckinHandler(r.service))
	r<PERSON><PERSON>(NewMemeTradeHandler(r.service))
	r.<PERSON>(NewPerpetualTradeHandler(r.service))
	r.RegisterHandler(NewMarketPageViewHandler(r.service))
	r.RegisterHandler(NewConsecutiveCheckinHandler(r.service))
	r.RegisterHandler(NewConsecutiveTradingDaysHandler(r.service))
	
	// Community task handlers
	r.RegisterHandler(NewTwitterFollowHandler(r.service))
	r.RegisterHandler(NewTwitterRetweetHandler(r.service))
	r.RegisterHandler(NewTwitterLikeHandler(r.service))
	r.RegisterHandler(NewTelegramJoinHandler(r.service))
	r.RegisterHandler(NewInviteFriendsHandler(r.service))
	r.RegisterHandler(NewShareReferralHandler(r.service))
	
	// Trading task handlers
	r.RegisterHandler(NewTradingPointsHandler(r.service))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading10K, 10000))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading50K, 50000))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading100K, 100000))
	r.RegisterHandler(NewAccumulatedTradingHandler(r.service, model.TaskIDAccumulatedTrading500K, 500000))
}

// RegisterHandler registers a task handler
func (r *TaskRegistry) RegisterHandler(handler TaskHandler) {
	r.handlers[handler.GetIdentifier()] = handler
}

// GetHandler returns a handler for the given task identifier
func (r *TaskRegistry) GetHandler(identifier model.TaskIdentifier) (TaskHandler, bool) {
	handler, exists := r.handlers[identifier]
	return handler, exists
}

// ProcessTask processes a task using the appropriate handler
func (r *TaskRegistry) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Try to get identifier from task
	var identifier model.TaskIdentifier
	
	// First, check if task has TaskIdentifier field (after migration)
	if task.TaskIdentifier != nil {
		identifier = *task.TaskIdentifier
	} else {
		// Fallback to name-based lookup for backward compatibility
		var exists bool
		identifier, exists = model.GetTaskIdentifierByName(task.Name)
		if !exists {
			return fmt.Errorf("unknown task: %s", task.Name)
		}
	}
	
	// Get handler for the identifier
	handler, exists := r.GetHandler(identifier)
	if !exists {
		return fmt.Errorf("no handler found for task identifier: %s", identifier)
	}
	
	// Process the task
	return handler.Handle(ctx, userID, task, data)
}

// ProcessTaskByIdentifier processes a task by its identifier
func (r *TaskRegistry) ProcessTaskByIdentifier(ctx context.Context, userID uuid.UUID, identifier model.TaskIdentifier, categoryName string, data map[string]interface{}) error {
	// Get tasks by category
	tasks, err := r.service.GetTasksByCategory(ctx, categoryName)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}
	
	// Find the specific task by identifier
	var targetTask *model.ActivityTask
	for _, task := range tasks {
		var taskIdentifier model.TaskIdentifier
		
		if task.TaskIdentifier != nil {
			taskIdentifier = *task.TaskIdentifier
		} else {
			// Fallback to name-based lookup
			var exists bool
			taskIdentifier, exists = model.GetTaskIdentifierByName(task.Name)
			if !exists {
				continue
			}
		}
		
		if taskIdentifier == identifier {
			targetTask = &task
			break
		}
	}
	
	if targetTask == nil {
		return fmt.Errorf("task not found: %s in category %s", identifier, categoryName)
	}
	
	return r.ProcessTask(ctx, userID, targetTask, data)
}

// ProcessTaskByName processes a task by name (for backward compatibility)
func (r *TaskRegistry) ProcessTaskByName(ctx context.Context, userID uuid.UUID, taskName, categoryName string, data map[string]interface{}) error {
	// Convert name to identifier
	identifier, exists := model.GetTaskIdentifierByName(taskName)
	if !exists {
		return fmt.Errorf("unknown task name: %s", taskName)
	}
	
	return r.ProcessTaskByIdentifier(ctx, userID, identifier, categoryName, data)
}

// GetAllHandlers returns all registered handlers
func (r *TaskRegistry) GetAllHandlers() map[model.TaskIdentifier]TaskHandler {
	return r.handlers
}
