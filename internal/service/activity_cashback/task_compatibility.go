package activity_cashback

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// TaskCompatibilityLayer provides backward compatibility for the old task system
type TaskCompatibilityLayer struct {
	registry         *TaskRegistry
	legacyProcessors *TaskProcessorManager
	service          ActivityCashbackServiceInterface
}

// NewTaskCompatibilityLayer creates a new compatibility layer
func NewTaskCompatibilityLayer(service ActivityCashbackServiceInterface) *TaskCompatibilityLayer {
	return &TaskCompatibilityLayer{
		registry:         NewTaskRegistry(service),
		legacyProcessors: NewTaskProcessorManager(service),
		service:          service,
	}
}

// ProcessTask processes a task using the new system with fallback to legacy
func (tcl *TaskCompatibilityLayer) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Try new registry-based approach first
	if task.TaskIdentifier != nil {
		global.GVA_LOG.Debug("Processing task with identifier",
			zap.String("identifier", string(*task.TaskIdentifier)),
			zap.String("task_name", task.Name))

		if err := tcl.registry.ProcessTask(ctx, userID, task, data); err == nil {
			return nil
		} else {
			global.GVA_LOG.Warn("Registry processing failed, falling back to legacy",
				zap.Error(err),
				zap.String("identifier", string(*task.TaskIdentifier)))
		}
	}

	// Try to map task name to identifier
	if identifier, exists := model.GetTaskIdentifierByName(task.Name); exists {
		global.GVA_LOG.Debug("Mapped task name to identifier",
			zap.String("task_name", task.Name),
			zap.String("identifier", string(identifier)))

		// Temporarily set the identifier for processing
		originalIdentifier := task.TaskIdentifier
		task.TaskIdentifier = &identifier

		if err := tcl.registry.ProcessTask(ctx, userID, task, data); err == nil {
			// Update the task in database with the identifier for future use
			tcl.updateTaskIdentifier(ctx, task.ID, identifier)
			return nil
		}

		// Restore original identifier if processing failed
		task.TaskIdentifier = originalIdentifier
		global.GVA_LOG.Warn("Identifier-based processing failed, falling back to legacy",
			zap.String("task_name", task.Name),
			zap.String("identifier", string(identifier)))
	}

	// Fallback to legacy processor system
	global.GVA_LOG.Debug("Using legacy processor system",
		zap.String("task_name", task.Name),
		zap.String("task_type", string(task.TaskType)))

	return tcl.legacyProcessors.ProcessTask(ctx, userID, task, data)
}

// ProcessTaskByName processes a task by name with intelligent routing
func (tcl *TaskCompatibilityLayer) ProcessTaskByName(ctx context.Context, userID uuid.UUID, taskName, categoryName string, data map[string]interface{}) error {
	// Try identifier-based approach first
	if identifier, exists := model.GetTaskIdentifierByName(taskName); exists {
		global.GVA_LOG.Debug("Processing task by name using identifier",
			zap.String("task_name", taskName),
			zap.String("identifier", string(identifier)))

		if err := tcl.registry.ProcessTaskByIdentifier(ctx, userID, identifier, categoryName, data); err == nil {
			return nil
		} else {
			global.GVA_LOG.Warn("Identifier-based processing by name failed, falling back to legacy",
				zap.Error(err),
				zap.String("task_name", taskName))
		}
	}

	// Fallback to legacy name-based processing
	global.GVA_LOG.Debug("Using legacy name-based processing",
		zap.String("task_name", taskName),
		zap.String("category", categoryName))

	return tcl.legacyProcessors.ProcessTaskByName(ctx, userID, taskName, categoryName, data)
}

// ProcessTradingEvent processes trading events with smart routing
func (tcl *TaskCompatibilityLayer) ProcessTradingEvent(ctx context.Context, userID uuid.UUID, data map[string]interface{}) error {
	tradeType, ok := data["trade_type"].(string)
	if !ok {
		return fmt.Errorf("invalid trade type in data")
	}

	// Process daily trading tasks using new system
	switch tradeType {
	case "MEME":
		// Try new identifier-based approach
		if err := tcl.registry.ProcessTaskByIdentifier(ctx, userID, model.TaskIDMemeTradeDaily, "daily", data); err == nil {
			global.GVA_LOG.Debug("Processed MEME trade using new system")
		} else {
			// Fallback to legacy approach
			global.GVA_LOG.Warn("New system failed for MEME trade, using legacy", zap.Error(err))
			if err := tcl.ProcessTaskByName(ctx, userID, "Complete one meme trade", "daily", data); err != nil {
				if err := tcl.ProcessTaskByName(ctx, userID, "Complete 1 MEME Trade", "daily", data); err != nil {
					global.GVA_LOG.Error("Failed to process MEME trade task", zap.Error(err))
				}
			}
		}

	case "PERPETUAL":
		// Try new identifier-based approach
		if err := tcl.registry.ProcessTaskByIdentifier(ctx, userID, model.TaskIDPerpetualTradeDaily, "daily", data); err == nil {
			global.GVA_LOG.Debug("Processed PERPETUAL trade using new system")
		} else {
			// Fallback to legacy approach
			global.GVA_LOG.Warn("New system failed for PERPETUAL trade, using legacy", zap.Error(err))
			if err := tcl.ProcessTaskByName(ctx, userID, "Complete one derivatives trade", "daily", data); err != nil {
				if err := tcl.ProcessTaskByName(ctx, userID, "Complete 1 Perpetual Trade", "daily", data); err != nil {
					global.GVA_LOG.Error("Failed to process perpetual trade task", zap.Error(err))
				}
			}
		}
	}

	// Process trading points task
	if err := tcl.registry.ProcessTaskByIdentifier(ctx, userID, model.TaskIDTradingPoints, "trading", data); err != nil {
		global.GVA_LOG.Warn("New system failed for trading points, using legacy", zap.Error(err))
		if err := tcl.ProcessTaskByName(ctx, userID, "Trading Points", "trading", data); err != nil {
			global.GVA_LOG.Error("Failed to process trading points task", zap.Error(err))
		}
	}

	// Process accumulated trading tasks
	accumulatedTasks := []struct {
		identifier model.TaskIdentifier
		newName    string
		oldName    string
	}{
		{model.TaskIDAccumulatedTrading10K, "Cumulative trading $10,000", "Accumulated Trading $10,000"},
		{model.TaskIDAccumulatedTrading50K, "Cumulative trading $50,000", "Accumulated Trading $50,000"},
		{model.TaskIDAccumulatedTrading100K, "Cumulative trading $100,000", "Accumulated Trading $100,000"},
		{model.TaskIDAccumulatedTrading500K, "Cumulative trading $500,000", "Accumulated Trading $500,000"},
	}

	for _, task := range accumulatedTasks {
		// Try new identifier-based approach
		if err := tcl.registry.ProcessTaskByIdentifier(ctx, userID, task.identifier, "trading", data); err != nil {
			global.GVA_LOG.Warn("New system failed for accumulated trading task, using legacy",
				zap.Error(err),
				zap.String("identifier", string(task.identifier)))

			// Fallback to legacy approach
			if err := tcl.ProcessTaskByName(ctx, userID, task.newName, "trading", data); err != nil {
				if err := tcl.ProcessTaskByName(ctx, userID, task.oldName, "trading", data); err != nil {
					global.GVA_LOG.Error("Failed to process accumulated trading task",
						zap.Error(err),
						zap.String("task", task.newName))
				}
			}
		}
	}

	return nil
}

// updateTaskIdentifier updates a task's identifier in the database
func (tcl *TaskCompatibilityLayer) updateTaskIdentifier(ctx context.Context, taskID uuid.UUID, identifier model.TaskIdentifier) {
	// This would update the task in the database with the identifier
	// Implementation depends on your repository pattern
	global.GVA_LOG.Info("Task identifier updated",
		zap.String("task_id", taskID.String()),
		zap.String("identifier", string(identifier)))
}

// GetCompatibilityStats returns statistics about system usage
func (tcl *TaskCompatibilityLayer) GetCompatibilityStats() map[string]interface{} {
	return map[string]interface{}{
		"registry_handlers":   len(tcl.registry.GetAllHandlers()),
		"legacy_processors":   len(tcl.legacyProcessors.processors),
		"identifier_mappings": len(model.LegacyTaskNameMap),
	}
}
